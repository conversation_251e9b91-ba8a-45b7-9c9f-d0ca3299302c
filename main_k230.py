#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230平台A4纸检测与紫色激光检测程序
移植自MaixPy平台，适配K230 CanMV固件API
所有功能集成在单个文件中，无外部依赖
"""

# K230平台核心导入
from media.sensor import Sensor
from media.display import Display
from media.media import *
import image
import time
import os
import gc
import numpy as np
from machine import UART, FPIOA
import threading
import re

# --------------------------- 全局配置参数 ---------------------------
# 摄像头配置
CAM_WIDTH, CAM_HEIGHT = 160, 120
DISPLAY_FPS = 60

# 显示配置
DISPLAY_WIDTH, DISPLAY_HEIGHT = 800, 480

# 紫色激光检测参数 (LAB色彩空间)
PIXEL_RADIUS = 3
# LAB色彩空间紫色阈值 (优化后的参数)
# 紫色在LAB空间中：L(亮度)中等，A(绿-红)偏红，B(蓝-黄)偏蓝
LOWER_PURPLE_LAB = (30, 80, 10, 60, -40, 10)  # (L_min, L_max, A_min, A_max, B_min, B_max)
LASER_MIN_AREA = 3      # 激光点最小面积（降低以检测更小的点）
LASER_MAX_AREA = 150    # 激光点最大面积
LASER_MIN_CIRCULARITY = 0.2  # 最小圆形度（降低以适应激光点形状）
LASER_MIN_BRIGHTNESS_L = 40  # LAB中L通道的最小亮度值

# A4纸检测参数 (LAB色彩空间白色)
# 白色在LAB空间中：L(亮度)高，A和B接近0
LOWER_WHITE_LAB = (70, 100, -15, 15, -15, 15)  # 白色LAB阈值（扩大范围以适应不同光照）
A4_MIN_AREA = 50        # A4纸最小面积
A4_MAX_AREA = 100000    # A4纸最大面积
A4_ASPECT_RATIO = 1.414 # A4纸标准长宽比
ASPECT_TOLERANCE = 0.2  # 长宽比容差

# 模式切换参数
MODE_A4 = 0             # A4纸检测模式
MODE_LASER = 1          # 激光检测模式
current_mode = MODE_A4  # 初始模式
SWITCH_INTERVAL = 1     # 切换间隔（帧数）

# 形状检测参数
DO_DILATION = True
DILATION_SIZE = 1
TRIANGLE_SCALE = 0.6
TRIANGLE_POS_X = 0.5
TRIANGLE_POS_Y = 0.5
POINTS_PER_EDGE = 3     # 每条边的点数量

# 显示参数
FONT_SCALE = 1
FONT_THICKNESS = 1

# 串口通信配置
UART_DEVICE = 0         # UART设备号
UART_BAUDRATE = 115200  # 波特率
UART_TX_PIN = 10        # TX引脚
UART_RX_PIN = 11        # RX引脚

# --------------------------- 内联串口通信库 ---------------------------
# 全局UART实例和变量绑定
_global_uart = None
_variable_bindings = {}
_bindings_lock = threading.Lock()

class Config:
    """配置管理类 - 内联自micu_uart_lib.config"""

    def __init__(self):
        # UART配置
        self.uart_device = UART_DEVICE
        self.uart_baudrate = UART_BAUDRATE

        # 缓冲区配置
        self.max_buffer_size = 1024

        # 刷新模式配置
        self.refresh_interval = 500  # ms

        # 帧格式配置
        self.frame_config = {
            "header": "$$",      # 默认帧头
            "tail": "##",        # 默认帧尾
            "enabled": True      # 启用帧检测
        }

    def set_frame_format(self, header="$$", tail="##", enabled=True):
        """设置数据帧格式"""
        self.frame_config["header"] = header
        self.frame_config["tail"] = tail
        self.frame_config["enabled"] = enabled

    def get_frame_config(self):
        """获取帧配置"""
        return self.frame_config.copy()

# 全局配置实例
config = Config()

def safe_decode(data, encoding='utf-8'):
    """安全解码字节数据"""
    if isinstance(data, str):
        return data
    if not isinstance(data, bytes):
        return str(data)
    try:
        return data.decode(encoding, errors='replace')
    except Exception:
        return str(data)

def get_timestamp():
    """获取当前时间戳"""
    return time.ticks_ms()

class Timer:
    """定时器类 - 内联自micu_uart_lib.utils"""

    def __init__(self, interval_ms):
        self.interval = interval_ms
        self.last_time = get_timestamp()

    def is_timeout(self):
        """检查是否超时"""
        current_time = get_timestamp()
        if current_time - self.last_time >= self.interval:
            self.last_time = current_time
            return True
        return False

    def reset(self):
        """重置定时器"""
        self.last_time = get_timestamp()

    def set_interval(self, interval_ms):
        """设置新的间隔"""
        self.interval = interval_ms

class VariableContainer:
    """变量容器类 - 内联自micu_uart_lib.utils"""

    def __init__(self, initial_value=None):
        self.value = initial_value
        self._lock = threading.Lock()

    def set(self, new_value):
        """设置值 - 线程安全"""
        with self._lock:
            self.value = new_value

    def get(self):
        """获取值 - 线程安全"""
        with self._lock:
            return self.value

    def __str__(self):
        with self._lock:
            return str(self.value)

    def __repr__(self):
        with self._lock:
            return f"VariableContainer({self.value})"

def set_global_uart(uart_instance):
    """设置全局UART实例"""
    global _global_uart
    _global_uart = uart_instance

def get_global_uart():
    """获取全局UART实例"""
    return _global_uart

def micu_printf(format_str, *args):
    """Printf风格函数，通过UART发送格式化数据"""
    try:
        # 格式化字符串
        if args:
            output = format_str % args
        else:
            output = str(format_str)

        # 控制台输出
        print(output)

        # 通过UART发送
        if _global_uart and _global_uart.is_initialized:
            # 添加小延迟防止缓冲区溢出
            time.sleep_ms(5)
            success = _global_uart.send(output)
            if not success:
                print("警告: UART发送失败")
            return success
        else:
            return False

    except Exception as e:
        # 格式错误的回退处理
        try:
            if args:
                safe_output = f"{format_str} {' '.join(str(arg) for arg in args)}"
            else:
                safe_output = str(format_str)

            print(safe_output)
            print(f"格式警告: {e}")

            if _global_uart and _global_uart.is_initialized:
                time.sleep_ms(5)
                return _global_uart.send(safe_output)
            return False
        except Exception as e2:
            print(f"micu_printf错误: {e2}")
            return False

def bind_variable(name, var_ref, var_type='auto'):
    """绑定变量名到变量引用 - 线程安全"""
    global _variable_bindings
    with _bindings_lock:
        _variable_bindings[name] = {
            'ref': var_ref,
            'type': var_type
        }
    print(f"变量绑定: {name} -> {var_type}")

def get_variable_bindings():
    """获取所有变量绑定 - 线程安全"""
    with _bindings_lock:
        return _variable_bindings.copy()

def clear_variable_bindings():
    """清除所有变量绑定 - 线程安全"""
    global _variable_bindings
    with _bindings_lock:
        _variable_bindings.clear()
    print("所有变量绑定已清除")

def parse_key_value_pairs(data_string):
    """解析字符串中的键值对"""
    result = {}

    # 移除常见分隔符并清理字符串
    cleaned = data_string.replace(',', ' ').replace(';', ' ').replace('，', ' ')

    # 匹配键值对的模式
    pattern = r'([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*([^\s:]+)'
    matches = re.findall(pattern, cleaned)

    for key, value in matches:
        # 尝试转换值为适当类型
        parsed_value = _parse_value(value)
        result[key] = parsed_value
        print(f"解析: {key} = {parsed_value} (类型: {type(parsed_value).__name__})")

    return result

def _parse_value(value_str):
    """解析字符串值为适当类型"""
    value_str = value_str.strip()

    # 尝试整数
    try:
        if '.' not in value_str and 'e' not in value_str.lower():
            return int(value_str)
    except ValueError:
        pass

    # 尝试浮点数
    try:
        return float(value_str)
    except ValueError:
        pass

    # 返回字符串
    return value_str

def apply_parsed_data(parsed_data):
    """将解析的数据应用到绑定变量 - 线程安全"""
    applied = {}

    # 线程安全地获取当前绑定
    with _bindings_lock:
        current_bindings = _variable_bindings.copy()

    for key, value in parsed_data.items():
        if key in current_bindings:
            binding = current_bindings[key]
            var_ref = binding['ref']
            var_type = binding['type']

            try:
                # 根据指定类型转换值
                if var_type == 'int':
                    converted_value = int(float(value))  # 处理 "123.0" -> 123
                elif var_type == 'float':
                    converted_value = float(value)
                elif var_type == 'str':
                    converted_value = str(value)
                else:  # auto
                    converted_value = value

                # 应用值到变量
                if isinstance(var_ref, dict) and 'key' in var_ref:
                    # 字典风格绑定
                    var_ref['container'][var_ref['key']] = converted_value
                elif hasattr(var_ref, 'set'):
                    # VariableContainer对象
                    var_ref.set(converted_value)
                elif hasattr(var_ref, '__setitem__'):
                    # 列表风格绑定
                    var_ref[0] = converted_value
                else:
                    # 直接赋值（注意：对不可变类型无效）
                    var_ref = converted_value

                applied[key] = converted_value
                print(f"应用: {key} = {converted_value}")

            except Exception as e:
                print(f"应用错误 {key}:{value}: {e}")
        else:
            print(f"警告: 变量 '{key}' 没有绑定")

    return applied

def extract_and_apply_data(buffer_data):
    """从缓冲区提取键值对并应用到绑定变量 - 线程安全"""
    print(f"从缓冲区提取数据: {buffer_data}")
    parsed = parse_key_value_pairs(buffer_data)
    applied = apply_parsed_data(parsed)
    return applied

class SimpleUART:
    """简单UART管理器 - 内联自micu_uart_lib.simple_uart"""

    def __init__(self):
        self.serial = None
        self.rx_buf = ""  # 接收缓冲区
        self.is_initialized = False
        self.refresh_timer = Timer(config.refresh_interval)
        self.auto_refresh = True  # 自动刷新模式
        self.auto_extract = False  # 自动提取键值对

        # 线程安全
        self._buffer_lock = threading.Lock()  # 缓冲区锁
        self._extract_lock = threading.Lock()  # 数据提取锁

    def init(self, device=None, baudrate=None, set_as_global=True):
        """初始化UART

        Args:
            device (int, optional): UART设备号，None使用配置默认值
            baudrate (int, optional): 波特率，None使用配置默认值
            set_as_global (bool): 是否设置为全局UART实例
        """
        try:
            # 使用提供的参数或配置默认值
            uart_device = device if device is not None else config.uart_device
            uart_baudrate = baudrate if baudrate is not None else config.uart_baudrate

            # 配置GPIO引脚功能
            fpioa = FPIOA()
            fpioa.set_function(UART_TX_PIN, FPIOA.UART0_TX)  # 设置TX引脚
            fpioa.set_function(UART_RX_PIN, FPIOA.UART0_RX)  # 设置RX引脚

            # 初始化UART
            self.serial = UART(uart_device, baudrate=uart_baudrate, bits=8, parity=None, stop=1)

            self.is_initialized = True

            # 设置为全局UART实例
            if set_as_global:
                set_global_uart(self)

            print(f"UART初始化成功 - 设备:{uart_device}, 波特率:{uart_baudrate}")
            if set_as_global:
                print("设置为全局UART实例，micu_printf可用")
            self._show_frame_config()

            return True

        except Exception as e:
            print(f"UART初始化失败: {str(e)}")
            self.is_initialized = False
            return False

    def set_frame(self, header="$$", tail="##", enabled=True):
        """设置全局帧头尾（用于所有发送/接收）

        Args:
            header (str): 帧头
            tail (str): 帧尾
            enabled (bool): 是否启用帧检测
        """
        config.set_frame_format(header, tail, enabled)
        print(f"帧格式: {header}...{tail} ({'启用' if enabled else '禁用'})")

    def set_auto_refresh(self, enabled=True):
        """设置自动刷新模式"""
        self.auto_refresh = enabled
        print(f"自动刷新: {'启用' if enabled else '禁用'}")

    def set_auto_extract(self, enabled=True):
        """设置自动数据提取模式"""
        self.auto_extract = enabled
        print(f"自动数据提取: {'启用' if enabled else '禁用'}")

    def _show_frame_config(self):
        """显示当前帧格式配置"""
        frame_config = config.get_frame_config()
        if frame_config["enabled"]:
            print(f"帧格式: {frame_config['header']}...{frame_config['tail']}")
        else:
            print("帧格式: 禁用（基于行的处理）")

    def send(self, data):
        """发送数据（使用全局帧头尾设置） - 增强重试机制

        Args:
            data (str): 要发送的数据
        """
        if not self.is_initialized:
            print("UART未初始化")
            return False

        try:
            # 确保输入数据格式正确
            if isinstance(data, bytes):
                data = data.decode('utf-8', errors='replace')
            else:
                data = str(data)

            frame_config = config.get_frame_config()

            if frame_config["enabled"]:
                # 使用全局配置的帧头尾
                frame_data = frame_config["header"] + data + frame_config["tail"]
            else:
                frame_data = data

            # 添加行结束符
            final_data = frame_data + "\r\n"

            # 重试机制发送
            max_retries = 3
            retry_delay = 10  # ms

            for attempt in range(max_retries):
                try:
                    # 发送数据
                    result = self.serial.write(final_data.encode())

                    # 检查写入是否成功
                    if result is not None and result >= 0:
                        return True
                    else:
                        if attempt < max_retries - 1:
                            print(f"发送尝试 {attempt + 1} 失败，重试中...")
                            time.sleep_ms(retry_delay)
                        else:
                            print(f"发送失败，已重试 {max_retries} 次")
                            return False

                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"发送尝试 {attempt + 1} 错误: {e}，重试中...")
                        time.sleep_ms(retry_delay)
                    else:
                        print(f"发送失败，已重试 {max_retries} 次: {e}")
                        return False

            return False

        except Exception as e:
            print(f"发送失败: {str(e)}")
            return False

    def receive(self):
        """接收一个完整数据帧 - 线程安全

        Returns:
            str or None: 提取的数据内容，None表示没有完整帧
        """
        # 检查是否有数据可读
        if self.serial and self.serial.any():
            try:
                data = self.serial.read()
                if data:
                    decoded_data = safe_decode(data)
                    if decoded_data:
                        with self._buffer_lock:
                            self._update_buffer(decoded_data)
            except Exception as e:
                print(f"接收错误: {e}")

        with self._buffer_lock:
            if not self.rx_buf:
                return None

            frame_config = config.get_frame_config()

            if not frame_config["enabled"]:
                # 如果帧检测禁用，直接返回缓冲区数据并清除
                data = self.rx_buf.strip()
                self.rx_buf = ""  # 立即清除缓冲区
                return data if data else None

            # 使用全局配置的帧头尾
            header = frame_config["header"]
            tail = frame_config["tail"]

            return self._extract_frame_with_delimiters(header, tail)

    def _update_buffer(self, data):
        """更新接收缓冲区 - 在锁定状态下调用"""
        if self.auto_refresh:
            # 直接刷新模式：用新数据替换缓冲区
            self.rx_buf = data
            print(f"缓冲区刷新 - 新数据: {data[:50] + '...' if len(data) > 50 else data}")
        else:
            # 累积模式：将数据添加到缓冲区
            if len(self.rx_buf) + len(data) > config.max_buffer_size:
                print("缓冲区满，用新数据替换")
                self.rx_buf = data
            else:
                self.rx_buf += data
                print(f"数据添加到缓冲区: {data}")

    def _extract_frame_with_delimiters(self, header, tail):
        """使用指定帧头尾提取数据 - 在锁定状态下调用"""
        # 查找帧头
        header_pos = self.rx_buf.find(header)
        if header_pos == -1:
            return None

        # 从帧头位置开始查找帧尾
        tail_pos = self.rx_buf.find(tail, header_pos + len(header))
        if tail_pos == -1:
            return None  # 没有找到完整帧

        # 从帧中提取数据（移除帧头尾）
        data = self.rx_buf[header_pos + len(header):tail_pos]

        # 直接清除整个缓冲区（刷新模式）
        self.rx_buf = ""

        return data.strip()

    def get_buffer(self):
        """获取原始缓冲区内容 - 线程安全"""
        with self._buffer_lock:
            return self.rx_buf

    def clear_buffer(self):
        """清除接收缓冲区 - 线程安全"""
        with self._buffer_lock:
            self.rx_buf = ""
        print("缓冲区已清除")

    def has_data(self):
        """检查缓冲区是否有数据 - 线程安全"""
        with self._buffer_lock:
            return len(self.rx_buf) > 0

    def close(self):
        """关闭UART"""
        if self.serial:
            try:
                self.serial.close()
                print("UART已关闭")
            except:
                pass

        self.is_initialized = False
        self.serial = None

# --------------------------- 摄像头和显示初始化 ---------------------------
def init_camera():
    """初始化K230摄像头"""
    try:
        print("正在初始化摄像头...")
        
        # 初始化媒体管道
        media_init()
        
        # 创建传感器对象
        sensor = Sensor(id=2)  # 使用sensor ID 2
        sensor.reset()
        
        # 配置传感器参数
        sensor.set_framesize(width=CAM_WIDTH, height=CAM_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)  # 使用RGB565格式优化性能
        
        # 启动传感器
        sensor.run()
        
        print(f"摄像头初始化成功 - 分辨率: {CAM_WIDTH}x{CAM_HEIGHT}, 格式: RGB565")
        return sensor
        
    except Exception as e:
        print(f"摄像头初始化失败: {e}")
        return None

def init_display():
    """初始化K230显示模块"""
    try:
        print("正在初始化显示模块...")
        
        # 初始化显示 - 使用虚拟显示模式
        Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
        
        print(f"显示模块初始化成功 - 分辨率: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
        return True
        
    except Exception as e:
        print(f"显示模块初始化失败: {e}")
        return False

def capture_image(sensor):
    """从传感器捕获图像"""
    try:
        # 捕获图像
        img = sensor.snapshot()
        if img is None:
            print("警告: 图像捕获失败")
            return None
            
        return img
        
    except Exception as e:
        print(f"图像捕获错误: {e}")
        return None

def display_image(img, x=0, y=0):
    """显示图像到屏幕"""
    try:
        if img is None:
            return False
            
        # 显示图像
        Display.show_image(img, x=x, y=y)
        return True
        
    except Exception as e:
        print(f"图像显示错误: {e}")
        return False

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    """紫色激光检测器 - 适配K230 image API"""
    
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        print(f"紫色激光检测器初始化 - 像素半径: {pixel_radius}")
    
    def detect(self, img):
        """检测紫色激光点
        
        Args:
            img: K230 image对象
            
        Returns:
            tuple: (处理后的图像, 检测到的激光点列表)
        """
        try:
            # 创建图像副本用于处理
            img_copy = img.copy()
            
            # 1. 使用LAB色彩空间进行紫色检测
            # K230的binary方法支持LAB阈值
            mask_purple = img_copy.binary([LOWER_PURPLE_LAB])

            # 2. 形态学操作去噪（优化参数）
            # 先用较小的核进行开运算去除小噪点
            mask_purple = mask_purple.open(size=1)
            # 再用闭运算连接可能断开的激光点区域
            mask_purple = mask_purple.close(size=2)
            
            # 3. 查找blob区域（优化参数）
            blobs = mask_purple.find_blobs([LOWER_PURPLE_LAB],
                                         area_threshold=LASER_MIN_AREA,
                                         pixels_threshold=5,  # 降低像素阈值以检测更小的点
                                         merge=True,
                                         margin=2)  # 设置合并边距
            
            best_laser_point = None
            best_score = -1
            
            # 4. 筛选最佳激光点
            for blob in blobs:
                area = blob.area()
                
                # a. 面积筛选
                if not (LASER_MIN_AREA <= area <= LASER_MAX_AREA):
                    continue
                
                # b. 圆形度筛选
                w, h = blob.w(), blob.h()
                if w == 0 or h == 0:
                    continue
                    
                # 近似圆形度计算
                perimeter = 2 * 3.14159 * ((w + h) / 4)
                if perimeter == 0:
                    continue
                circularity = 4 * 3.14159 * area / (perimeter * perimeter)
                
                if circularity < LASER_MIN_CIRCULARITY:
                    continue
                
                # c. 获取中心点坐标
                cx, cy = blob.cx(), blob.cy()
                
                # d. 亮度筛选 (改进的亮度检测)
                # 在原图上检查该点的亮度
                try:
                    pixel_value = img.get_pixel(cx, cy)
                    if isinstance(pixel_value, tuple) and len(pixel_value) >= 3:
                        # RGB565转换为亮度值的近似计算
                        r, g, b = pixel_value[:3]
                        brightness = 0.299 * r + 0.587 * g + 0.114 * b
                    else:
                        brightness = pixel_value if isinstance(pixel_value, (int, float)) else 0

                    # 使用相对亮度阈值，适应不同光照条件
                    if brightness < LASER_MIN_BRIGHTNESS_L:
                        continue

                except Exception:
                    # 如果获取像素值失败，使用blob的密度作为替代指标
                    brightness = blob.density() * 100  # 密度转换为亮度近似值
                
                # e. 计算评分
                score = brightness  # 使用亮度作为评分
                
                # f. 选择最佳点
                if score > best_score:
                    best_score = score
                    best_laser_point = (cx, cy)
            
            # 5. 绘制检测结果
            if best_laser_point is not None:
                cx, cy = best_laser_point
                # 绘制激光点标记（紫色圆圈）
                img_copy.draw_circle(cx, cy, self.pixel_radius, color=(255, 0, 255), fill=True)
                img_copy.draw_string(cx - 20, cy - 10, "Purple", color=(255, 0, 255), scale=1)
                return img_copy, [best_laser_point]
            
            # 6. 没有找到激光点
            return img_copy, []
            
        except Exception as e:
            print(f"激光检测错误: {e}")
            return img, []

# --------------------------- A4纸检测功能 ---------------------------
def detect_a4_paper(img, img_display):
    """A4纸检测和点生成功能

    Args:
        img: 原始图像
        img_display: 显示图像

    Returns:
        tuple: (处理后的显示图像, A4纸数量, 生成的点坐标列表)
    """
    try:
        a4_count = 0
        generated_points = []

        # 1. 使用LAB色彩空间进行白色检测
        white_mask = img.binary([LOWER_WHITE_LAB])

        # 2. 形态学操作减少噪点
        if DO_DILATION:
            white_mask = white_mask.dilate(size=DILATION_SIZE)
            white_mask = white_mask.erode(size=DILATION_SIZE)

        # 3. 查找白色区域（A4纸候选）
        blobs = white_mask.find_blobs([LOWER_WHITE_LAB],
                                    area_threshold=A4_MIN_AREA,
                                    pixels_threshold=20,
                                    merge=True)

        # 4. 筛选A4纸候选区域
        a4_candidates = []
        for blob in blobs:
            area = blob.area()

            # 过滤面积不在范围内的区域
            if not (A4_MIN_AREA <= area <= A4_MAX_AREA):
                continue

            # 获取边界框
            x, y, w, h = blob.rect()

            # 计算长宽比
            if w == 0 or h == 0:
                continue
            aspect_ratio = max(w, h) / min(w, h)

            # 检查是否接近A4纸的长宽比
            if abs(aspect_ratio - A4_ASPECT_RATIO) <= ASPECT_TOLERANCE:
                a4_candidates.append({
                    'blob': blob,
                    'area': area,
                    'rect': (x, y, w, h),
                    'aspect_ratio': aspect_ratio
                })

        # 5. 处理找到的A4纸（选择最大的一个）
        if a4_candidates:
            # 按面积排序，选择最大的A4纸候选区域
            a4_candidates.sort(key=lambda c: c['area'], reverse=True)
            best_candidate = a4_candidates[0]
            a4_count = 1

            blob = best_candidate['blob']
            x, y, w, h = best_candidate['rect']

            # 绘制A4纸边界框和中心
            img_display.draw_rectangle(x, y, w, h, color=(0, 255, 0), thickness=2)

            # 计算中心点
            center_x = x + w // 2
            center_y = y + h // 2
            img_display.draw_circle(center_x, center_y, 3, color=(0, 0, 255), fill=True)
            img_display.draw_string(x, y - 15, "A4 Paper", color=(255, 255, 255), scale=1)

            # 生成三角形点（在A4纸区域内）
            generated_points = generate_triangle_points(x, y, w, h)

            # 绘制生成的点
            if generated_points:
                # 发送点坐标到串口
                point_count = len(generated_points)
                point_str = ",".join([f"{int(x)},{int(y)}" for x, y in generated_points])
                micu_printf(f"M,{point_count},{point_str}")

                # 绘制点（蓝色，2像素）
                for px, py in generated_points:
                    img_display.draw_circle(int(px), int(py), 2, color=(255, 0, 0), fill=True)

        return img_display, a4_count, generated_points

    except Exception as e:
        print(f"A4纸检测错误: {e}")
        return img_display, 0, []

def generate_triangle_points(x, y, w, h):
    """在指定区域内生成三角形点

    Args:
        x, y: 区域左上角坐标
        w, h: 区域宽度和高度

    Returns:
        list: 生成的点坐标列表
    """
    try:
        # 计算三角形中心位置
        tri_center_x = x + int(w * TRIANGLE_POS_X)
        tri_center_y = y + int(h * TRIANGLE_POS_Y)

        # 计算三角形大小
        tri_size = int(min(w, h) * TRIANGLE_SCALE)
        tri_h = int(tri_size * 0.866)  # 等边三角形的高

        # 三角形三个顶点
        tri_pts = [
            (tri_center_x, tri_center_y - tri_h // 2),                    # 顶点
            (tri_center_x - tri_size // 2, tri_center_y + tri_h // 2),    # 左下
            (tri_center_x + tri_size // 2, tri_center_y + tri_h // 2)     # 右下
        ]

        # 生成三角形边上的点
        generated_points = []
        for i in range(3):
            p1 = tri_pts[i]
            p2 = tri_pts[(i + 1) % 3]

            for j in range(POINTS_PER_EDGE + 1):
                t = j / POINTS_PER_EDGE
                px = p1[0] + t * (p2[0] - p1[0])
                py = p1[1] + t * (p2[1] - p1[1])
                generated_points.append((px, py))

        return generated_points

    except Exception as e:
        print(f"生成三角形点错误: {e}")
        return []

# --------------------------- 主程序入口 ---------------------------
def main():
    """主程序入口"""
    print("=" * 50)
    print("K230 A4纸检测与紫色激光检测程序启动...")
    print(f"分辨率: {CAM_WIDTH}x{CAM_HEIGHT}")
    print(f"每条边点数量: {POINTS_PER_EDGE}，点大小: 3像素")
    print("=" * 50)
    
    # 禁用垃圾回收以提升性能
    gc.disable()
    
    # 初始化硬件
    sensor = init_camera()
    if sensor is None:
        print("摄像头初始化失败，程序退出")
        return
    
    if not init_display():
        print("显示模块初始化失败，程序退出")
        return
    
    # 初始化检测器
    detector = PurpleLaserDetector(pixel_radius=PIXEL_RADIUS)

    # 初始化串口
    uart = SimpleUART()
    if uart.init(UART_DEVICE, UART_BAUDRATE, set_as_global=True):
        print("串口初始化成功")
    else:
        print("串口初始化失败")
        # 继续运行，但串口功能不可用

    # 设置帧格式
    uart.set_frame("$$", "##", True)  # 设置帧头尾为 $$...##

    # 初始化统计变量
    frame_count = 0
    last_time = time.time()
    fps_update_time = last_time
    fps_frame_count = 0
    current_fps = 0
    generated_points = []  # 存储生成的点坐标

    # 性能监控变量
    max_fps = 0
    min_fps = float('inf')
    total_frames = 0
    error_count = 0

    print("初始化完成，开始主循环...")
    print(f"目标FPS: {DISPLAY_FPS}, 模式切换间隔: {SWITCH_INTERVAL}帧")
    
    try:
        # 主循环
        while not os.exitpoint():
            frame_count += 1
            total_frames += 1
            fps_frame_count += 1

            # 每SWITCH_INTERVAL帧切换一次模式
            if frame_count % SWITCH_INTERVAL == 0:
                old_mode = current_mode
                current_mode = MODE_LASER if current_mode == MODE_A4 else MODE_A4
                if old_mode != current_mode:
                    mode_name = "A4检测" if current_mode == MODE_A4 else "激光检测"
                    print(f"模式切换: {mode_name}模式")
            
            # 捕获图像
            try:
                img = capture_image(sensor)
                if img is None:
                    error_count += 1
                    if error_count % 10 == 0:
                        print(f"警告: 图像捕获失败次数: {error_count}")
                    continue

                # 创建显示图像副本
                img_display = img.copy()

                # 初始化检测结果
                a4_count = 0
                laser_points = []

            except Exception as e:
                error_count += 1
                print(f"图像捕获异常: {e}")
                continue
            
            # 根据当前模式进行检测
            if current_mode == MODE_A4:
                # A4纸检测模式
                img_display, a4_count, generated_points = detect_a4_paper(img, img_display)
                
            else:
                # 激光检测模式
                img_display, laser_points = detector.detect(img)
                
                # 显示激光检测信息
                if laser_points:
                    x, y = laser_points[0]
                    print(f"检测到激光点: ({x}, {y})")
                    # 发送激光点坐标到串口
                    micu_printf(f"P,{x},{y}")
            
            # 计算并显示FPS（优化的FPS计算）
            current_time = time.time()

            # 每秒更新一次FPS显示
            if current_time - fps_update_time >= 1.0:
                if fps_frame_count > 0:
                    current_fps = int(fps_frame_count / (current_time - fps_update_time))

                    # 更新性能统计
                    if current_fps > max_fps:
                        max_fps = current_fps
                    if current_fps < min_fps and current_fps > 0:
                        min_fps = current_fps

                    # 重置FPS计数器
                    fps_frame_count = 0
                    fps_update_time = current_time
                else:
                    current_fps = 0
            
            # 显示统计信息（增强版）
            if current_mode == MODE_A4:
                stats_text = f"FPS:{current_fps} | A4:{a4_count} | Points:{len(generated_points) if 'generated_points' in locals() else 0}"
            else:
                stats_text = f"FPS:{current_fps} | Lasers:{len(laser_points)}"

            img_display.draw_string(10, 20, stats_text, color=(255, 255, 255), scale=1)

            # 显示当前模式和帧计数
            mode_text = "Mode: A4 Detection" if current_mode == MODE_A4 else "Mode: Laser Detection"
            img_display.draw_string(10, 35, mode_text, color=(0, 255, 255), scale=1)

            # 显示性能信息（每100帧显示一次）
            if frame_count % 100 == 0 and max_fps > 0:
                perf_text = f"Max:{max_fps} Min:{min_fps} Err:{error_count}"
                img_display.draw_string(10, 50, perf_text, color=(255, 255, 0), scale=1)
            
            # 显示图像
            try:
                display_image(img_display)
            except Exception as e:
                print(f"图像显示错误: {e}")
                error_count += 1

            # 定期垃圾回收和性能监控
            if frame_count % 100 == 0:
                gc.collect()

                # 性能监控日志
                avg_fps = total_frames / (current_time - last_time) if (current_time - last_time) > 0 else 0
                print(f"性能统计 - 总帧数:{total_frames}, 平均FPS:{avg_fps:.1f}, 错误:{error_count}")

                # 内存使用监控
                try:
                    import gc
                    mem_free = gc.mem_free() if hasattr(gc, 'mem_free') else "N/A"
                    mem_alloc = gc.mem_alloc() if hasattr(gc, 'mem_alloc') else "N/A"
                    print(f"内存状态 - 空闲:{mem_free}, 已分配:{mem_alloc}")
                except:
                    pass
                
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        print(f"运行统计 - 总帧数:{total_frames}, 平均FPS:{total_frames/(time.time()-last_time):.1f}")
    except Exception as e:
        print(f"主循环错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        print("正在清理资源...")
        cleanup_success = True

        try:
            if 'sensor' in locals() and sensor:
                sensor.stop()
                print("摄像头已停止")
        except Exception as e:
            print(f"摄像头清理错误: {e}")
            cleanup_success = False

        try:
            if 'uart' in locals() and uart:
                uart.close()
                print("串口已关闭")
        except Exception as e:
            print(f"串口清理错误: {e}")
            cleanup_success = False

        try:
            media_deinit()
            print("媒体管道已清理")
        except Exception as e:
            print(f"媒体清理错误: {e}")
            cleanup_success = False

        # 最终统计
        if total_frames > 0:
            final_time = time.time()
            total_runtime = final_time - last_time
            avg_fps = total_frames / total_runtime if total_runtime > 0 else 0
            print(f"最终统计:")
            print(f"  运行时间: {total_runtime:.1f}秒")
            print(f"  总帧数: {total_frames}")
            print(f"  平均FPS: {avg_fps:.1f}")
            print(f"  最高FPS: {max_fps}")
            print(f"  最低FPS: {min_fps if min_fps != float('inf') else 0}")
            print(f"  错误次数: {error_count}")

        if cleanup_success:
            print("程序正常结束")
        else:
            print("程序结束（部分清理失败）")

# --------------------------- 程序入口 ---------------------------
if __name__ == "__main__":
    main()
