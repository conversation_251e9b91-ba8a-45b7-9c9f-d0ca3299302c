# K230 API映射方案设计文档

## 1. 映射方案总览

本文档基于K230 CanMV固件API，设计了从MaixPy到K230的完整API映射方案，确保功能完整性和性能优化。

### 1.1 映射原则
- **功能等价性**：确保K230 API能完全替代原有功能
- **性能优化**：选择最适合K230硬件的API和参数
- **兼容性保证**：保持原有接口语义和行为
- **资源效率**：优化内存使用和处理速度

## 2. 核心模块映射方案

### 2.1 摄像头模块映射

#### 原始MaixPy API
```python
from maix import camera, image
cam = camera.Camera(CAM_WIDTH, CAM_HEIGHT, image.Format.FMT_BGR888, fps=DISPLAY_FPS)
img = cam.read()
```

#### K230 CanMV API映射
```python
from media.sensor import Sensor
from media.media import *

# 初始化媒体管道
media_init()

# 配置传感器
sensor = Sensor(id=2)  # 使用sensor ID 2
sensor.reset()
sensor.set_framesize(width=160, height=120)
sensor.set_pixformat(Sensor.RGB565)  # 使用RGB565优化性能
sensor.run()

# 图像采集
img = sensor.snapshot()
```

**映射要点：**
- 使用RGB565格式替代BGR888，提升性能
- 需要调用media_init()初始化媒体管道
- sensor.reset()确保传感器正确初始化

### 2.2 显示模块映射

#### 原始MaixPy API
```python
from maix import display
disp = display.Display()
disp.show(img_show)
```

#### K230 CanMV API映射
```python
from media.display import Display

# 初始化显示
Display.init(Display.VIRT, width=800, height=480)

# 显示图像
Display.show_image(img, x=0, y=0)
```

**映射要点：**
- 使用虚拟显示模式(Display.VIRT)
- 支持指定显示位置(x, y)
- 自动处理图像格式转换

### 2.3 图像处理模块映射

#### 原始MaixPy API
```python
from maix import image
img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
img_show = image.cv2image(img_display, bgr=True, copy=False)
```

#### K230 CanMV API映射
```python
# K230中直接使用image对象，无需格式转换
# img已经是可处理的image.Image对象
# 直接调用image对象的方法进行处理
```

**映射要点：**
- 消除格式转换开销
- 直接在image对象上操作
- 支持链式调用优化代码

### 2.4 时间模块映射

#### 原始MaixPy API
```python
from maix import time
current_time = time.time()
time.sleep_ms(5)
```

#### K230 CanMV API映射
```python
import time
# 或者使用 import utime

current_time = time.time()
time.sleep_ms(5)  # 保持API一致性
```

**映射要点：**
- API完全兼容，无需修改
- 可选择使用utime模块优化性能

### 2.5 应用控制映射

#### 原始MaixPy API
```python
from maix import app
while not app.need_exit():
    # 主循环逻辑
```

#### K230 CanMV API映射
```python
import os
while not os.exitpoint():
    # 主循环逻辑
```

**映射要点：**
- 语义完全一致
- 支持相同的退出机制

### 2.6 串口模块映射

#### 原始MaixPy API
```python
from maix import uart
self.serial = uart.UART(uart_device, uart_baudrate)
self.serial.set_received_callback(self._on_received)
result = self.serial.write_str(final_data)
```

#### K230 CanMV API映射
```python
from machine import UART, FPIOA

# 配置GPIO引脚功能
fpioa = FPIOA()
fpioa.set_function(10, FPIOA.UART0_TX)  # GPIO10 -> UART0_TX
fpioa.set_function(11, FPIOA.UART0_RX)  # GPIO11 -> UART0_RX

# 初始化UART
self.serial = UART(0, baudrate=115200, bits=8, parity=None, stop=1)

# 数据发送
result = self.serial.write(final_data.encode())

# 数据接收（需要轮询或中断处理）
if self.serial.any():
    data = self.serial.read()
```

**映射要点：**
- 需要FPIOA配置GPIO引脚功能
- 回调机制需要改为轮询或中断处理
- 数据需要编码为bytes格式

## 3. OpenCV功能替代方案

### 3.1 颜色空间转换

#### 原始OpenCV
```python
hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
```

#### K230替代方案
```python
# K230的image对象支持LAB色彩空间
# 直接在image对象上操作，无需显式转换
# 颜色检测将使用LAB色彩空间的阈值
```

**技术说明：**
- K230原生支持LAB色彩空间
- LAB空间在颜色检测上更加稳定
- 需要将HSV阈值转换为LAB阈值

### 3.2 颜色范围检测

#### 原始OpenCV
```python
mask_purple = cv2.inRange(hsv, LOWER_PURPLE, UPPER_PURPLE)
```

#### K230替代方案
```python
# 将HSV阈值转换为LAB阈值
# LOWER_PURPLE = [54, 30, 56] -> LAB equivalent
# UPPER_PURPLE = [230, 178, 255] -> LAB equivalent
mask_purple = img.binary([(l_lo, l_hi, a_lo, a_hi, b_lo, b_hi)])
```

**参数转换：**
- 需要建立HSV到LAB的阈值映射表
- 可能需要实验调整获得最佳效果

### 3.3 形态学操作

#### 原始OpenCV
```python
mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_OPEN, kernel)
mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, kernel)
```

#### K230替代方案
```python
mask_purple = mask_purple.open(size=1)   # 开运算去噪
mask_purple = mask_purple.close(size=1)  # 闭运算连接
```

**映射说明：**
- size参数对应kernel大小
- K230提供了更简洁的API

### 3.4 轮廓检测与分析

#### 原始OpenCV
```python
contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
for cnt in contours:
    area = cv2.contourArea(cnt)
    perimeter = cv2.arcLength(cnt, True)
    rect = cv2.minAreaRect(cnt)
    cx, cy = map(int, rect[0])
```

#### K230替代方案
```python
blobs = mask.find_blobs([threshold], 
                       area_threshold=LASER_MIN_AREA,
                       pixels_threshold=10,
                       merge=True)
for blob in blobs:
    area = blob.area()
    cx, cy = blob.cx(), blob.cy()
    w, h = blob.w(), blob.h()
    # 圆形度计算：4*π*area/(perimeter^2)
    perimeter = 2 * 3.14159 * ((w + h) / 4)  # 近似计算
    circularity = 4 * 3.14159 * area / (perimeter * perimeter)
```

**优势：**
- find_blobs直接提供面积、中心点等信息
- 内置面积阈值筛选
- 性能更优

### 3.5 绘图功能

#### 原始OpenCV
```python
cv2.circle(img, (cx, cy), radius, (255, 0, 255), -1)
cv2.putText(img, "Purple", (cx-20, cy-10), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
cv2.drawContours(img_display, [contour], -1, (0, 255, 0), 1)
cv2.rectangle(img, (x, y, w, h), (0, 255, 0), 2)
```

#### K230替代方案
```python
img.draw_circle(cx, cy, radius, color=(255, 0, 255), fill=True)
img.draw_string(cx-20, cy-10, "Purple", color=(255, 0, 255), scale=1)
# 轮廓绘制需要逐点连线
for i in range(len(contour_points)):
    p1 = contour_points[i]
    p2 = contour_points[(i+1) % len(contour_points)]
    img.draw_line(p1[0], p1[1], p2[0], p2[1], color=(0, 255, 0))
img.draw_rectangle(x, y, w, h, color=(0, 255, 0), thickness=2)
```

## 4. 性能优化策略

### 4.1 内存管理优化
```python
# 使用MMZ内存分配
img = image.Image(width, height, image.RGB565, alloc=image.ALLOC_MMZ)

# 复用图像对象
img.clear()  # 清空而不是重新分配
```

### 4.2 图像格式优化
- **RGB565**: 平衡性能和质量，内存占用减半
- **GRAYSCALE**: 单通道处理，适用于形状检测
- **ARGB8888**: 高质量显示，适用于最终输出

### 4.3 处理流水线优化
```python
# 链式调用减少中间变量
result = img.binary(thresholds).open(1).close(1).find_blobs([threshold])

# 原地操作减少内存拷贝
img.draw_circle(x, y, r, color, fill=True)  # 直接修改原图
```

## 5. 兼容性保证

### 5.1 参数映射表
| 原始参数 | K230参数 | 说明 |
|---------|----------|------|
| BGR888 | RGB565 | 颜色格式转换 |
| HSV阈值 | LAB阈值 | 色彩空间转换 |
| kernel大小 | size参数 | 形态学操作 |
| 轮廓面积 | blob.area() | 区域分析 |

### 5.2 行为一致性
- 保持相同的检测精度
- 维持相同的输出格式
- 确保相同的错误处理机制

## 6. 风险评估与缓解

### 6.1 主要风险
1. **颜色检测精度**：HSV到LAB转换可能影响检测效果
2. **性能差异**：API差异可能导致性能变化
3. **内存使用**：不同的内存管理策略

### 6.2 缓解措施
1. **参数调优**：提供可配置的阈值参数
2. **性能监控**：实时FPS监控和优化
3. **内存监控**：定期垃圾回收和内存检查

## 7. 实施计划

### 7.1 优先级排序
1. **高优先级**：摄像头、显示、基础图像处理
2. **中优先级**：OpenCV功能替代、串口通信
3. **低优先级**：性能优化、错误处理完善

### 7.2 验证标准
- 功能完整性：所有原有功能正常工作
- 性能指标：帧率不低于原版本
- 稳定性：长时间运行无异常
- 兼容性：与外部设备通信正常
